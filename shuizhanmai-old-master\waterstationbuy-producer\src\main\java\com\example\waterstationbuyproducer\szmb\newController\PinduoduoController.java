package com.example.waterstationbuyproducer.szmb.newController;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.doudian.open.utils.JsonUtil;
import com.example.waterstationbuyproducer.dao.*;
import com.example.waterstationbuyproducer.easyexcel.EasyExcelListener;
import com.example.waterstationbuyproducer.easyexcel.EasyExcelUtils;
import com.example.waterstationbuyproducer.easyexcel.ExcelCheckErrDto;
import com.example.waterstationbuyproducer.entity.OrderSource;
import com.example.waterstationbuyproducer.entity.SmsMaster;
import com.example.waterstationbuyproducer.entity.SmsRecord;
import com.example.waterstationbuyproducer.entity.SmsRelevance;
import com.example.waterstationbuyproducer.entity.SmzCOrderDetails;
import com.example.waterstationbuyproducer.entity.SmzCOrderReturns;
import com.example.waterstationbuyproducer.entity.StoreMsg;
import com.example.waterstationbuyproducer.entity.StoreSmsInfo;
import com.example.waterstationbuyproducer.entity.SzmCAddress;
import com.example.waterstationbuyproducer.entity.SzmCOrderMain;
import com.example.waterstationbuyproducer.entity.SzmCStoreApplyFor;
import com.example.waterstationbuyproducer.entity.SzmCUser;
import com.example.waterstationbuyproducer.entity.SzmCUserinfo;
import com.example.waterstationbuyproducer.szmb.daoru.PinduoduoOrderImportService;
import com.example.waterstationbuyproducer.szmb.daoru.SfOrderImportService;
import com.example.waterstationbuyproducer.szmb.dto.PinduoduoOrderDto;
import com.example.waterstationbuyproducer.szmb.dto.PinduoduoOrderDtoErrDto;
import com.example.waterstationbuyproducer.szmb.service.hz.order.SzmBOrderService;
import com.example.waterstationbuyproducer.szmb.service.order.OrderSourceService;
import com.example.waterstationbuyproducer.szmc.controller.IntegeralVoController;
import com.example.waterstationbuyproducer.szmc.service.SzmCUserService;
import com.example.waterstationbuyproducer.util.DateUtil;
import com.example.waterstationbuyproducer.util.DateUtils;
import com.example.waterstationbuyproducer.util.ResultBean;
import com.example.waterstationbuyproducer.util.StoreIdUtil;
import com.example.waterstationbuyproducer.util.logger.LoggerUtil;
import com.example.waterstationbuyproducer.util.redis.RedisUtil;
import com.example.waterstationbuyproducer.util.sms.RemindSMS;
import com.example.waterstationbuyproducer.util.sms.UtilSMS;
import com.pdd.pop.sdk.http.PopAccessTokenClient;
import com.pdd.pop.sdk.http.PopClient;
import com.pdd.pop.sdk.http.PopHttpClient;
import com.pdd.pop.sdk.http.api.pop.request.PddCloudIsvPageCodeRequest;
import com.pdd.pop.sdk.http.api.pop.request.PddLogisticsCompaniesGetRequest;
import com.pdd.pop.sdk.http.api.pop.request.PddOpenDecryptBatchRequest;
import com.pdd.pop.sdk.http.api.pop.request.PddOpenDecryptBatchRequest.DataListItem;
import com.pdd.pop.sdk.http.api.pop.request.PddOrderListGetRequest;
import com.pdd.pop.sdk.http.api.pop.request.PddPopAuthTokenCreateRequest;
import com.pdd.pop.sdk.http.api.pop.response.PddCloudIsvPageCodeResponse;
import com.pdd.pop.sdk.http.api.pop.response.PddLogisticsCompaniesGetResponse;
import com.pdd.pop.sdk.http.api.pop.response.PddOpenDecryptBatchResponse;
import com.pdd.pop.sdk.http.api.pop.response.PddOpenDecryptBatchResponse.OpenDecryptBatchResponseDataDecryptListItem;
import com.pdd.pop.sdk.http.api.pop.response.PddOrderListGetResponse;
import com.pdd.pop.sdk.http.api.pop.response.PddOrderListGetResponse.OrderListGetResponseOrderListItem;
import com.pdd.pop.sdk.http.api.pop.response.PddPopAuthTokenCreateResponse;

import cn.hutool.core.lang.Validator;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.Optional;

/**
 * @author: cjy
 * @create: 2025/3/4
 * @Description:
 * @FileName: PinduoduoController
 * @History:
 * @自定义内容：
 */
@RestController
@RequestMapping("pdd")
public class PinduoduoController {

    private final IntegeralVoController integeralVoController;
    protected static final Log logger = LogFactory.getLog(PinduoduoController.class);

    @Autowired
    private SzmCUserMapper szmCUserMapper;
    @Autowired
    private SzmCUserService szmCUserService;
    @Autowired
    private SzmCAddressMapper szmCAddressMapper;
    @Autowired
    private SzmCOrderMainMapper szmCOrderMainMapper;
    @Autowired
    private SmzCOrderDetailsMapper smzCOrderDetailsMapper;
    @Autowired
    private SzmBOrderService szmBOrderService;
    
    @Autowired
    private OrderSourceService orderSourceService;

    @Autowired
    private StoreSmsInfoMapper storeSmsInfoMapper;

    @Autowired
    private SmsRelevanceMapper smsRelevanceMapper;

    @Autowired
    private SmsMasterMapper smsMasterMapper;

    @Autowired
    private SmsRecordMapper smsRecordMapper;

    @Autowired
    private StoreMsgMapper storeMsgMapper;

    @Autowired
    private SzmCUserinfoMapper szmCUserinfoMapper;

    @Autowired
    private SzmCStoreApplyForMapper szmCStoreApplyForMapper;
    @Autowired
    private SmzCOrderReturnsMapper smzCOrderReturnsMapper;
    @Autowired
    private PinduoduoOrderImportService pinduoduoOrderImportService;
    @Autowired
    private SfOrderImportService sfOrderImportService;
    @Autowired
    private RedisUtil redisUtil;

    private String clientId = "13500223dc96495d90acd3f02a827a06";
    private String clientSecret = "0bf9ee1b3b3d21aedce0e760d7f7ad760ccfda8f";
    private String accessToken = "a436d64db9a944cb918ee74bf7b5b22958207d48";

    private Integer pageSize = 10;
    @Autowired
    private StoreIdUtil storeIdUtil;

    PinduoduoController(IntegeralVoController integeralVoController) {
        this.integeralVoController = integeralVoController;
    }

    @RequestMapping("notify")
    public ResultBean notify(@RequestParam Map<String, Object> param) {
        logger.error("拼多多回调通知：" + param);
        return new ResultBean().success(param);
    }

    @GetMapping("getAccessToken")
    public String getAccessToken() throws Exception {
        // 从Redis缓存中获取token
        Object accessToken = redisUtil.get("pdd_access_token");
        if (accessToken != null) {
            return accessToken.toString();
        }
        PopClient client = new PopHttpClient(clientId, clientSecret);

        PddPopAuthTokenCreateRequest request = new PddPopAuthTokenCreateRequest();
        request.setCode("1221031eb8b34eb98d64d5850c371337a0c106c6");
        PddPopAuthTokenCreateResponse response = client.syncInvoke(request);
        System.out.println(response.getPopAuthTokenCreateResponse());
        String accessTokenNew = response.getPopAuthTokenCreateResponse().getAccessToken();
        // Redis缓存token,过期时间1年
        redisUtil.set("pdd_access_token", accessTokenNew, 365 * 24 * 60 * 60);
        return accessTokenNew;
    }

    @GetMapping("isvPageCode")
    public String isvPageCode() throws Exception {
        PopClient client = new PopHttpClient(clientId, clientSecret);

        PddCloudIsvPageCodeRequest request = new PddCloudIsvPageCodeRequest();
        request.setHttpReferer("https://waterstation.com.cn:10000/pinduoduo/notify");
        List<String> mallIdList = new ArrayList<String>();
        mallIdList.add("425156698");
        request.setMallIdList(mallIdList);
        request.setUserId("425156698");
        PddCloudIsvPageCodeResponse response = client.syncInvoke(request);

        return response.getResponse().getPageCode();
    }

    @GetMapping("getOrder")
    public ResultBean getOrder(String startTime, String endTime, Integer pageNo) throws Exception {

        int pageNow = pageNo == null ? 1 : pageNo;
        PopClient client = new PopHttpClient(clientId, clientSecret);

        PddOrderListGetRequest request = new PddOrderListGetRequest();
        request.setStartConfirmAt(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(startTime).getTime() / 1000);
        request.setEndConfirmAt(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(endTime).getTime() / 1000);
        request.setOrderStatus(5);
        request.setPage(pageNow);
        request.setPageSize(pageSize);
        request.setRefundStatus(5);
        request.setUseHasNext(true);
        PddOrderListGetResponse response = client.syncInvoke(request, getAccessToken());
        
        if(response.getErrorResponse() != null){
            logger.error("拼多多订单列表：" + JSON.toJSONString(response));
            return new ResultBean().error(response.getErrorResponse().getErrorMsg());
        }
        List<OrderListGetResponseOrderListItem> orderList = response.getOrderListGetResponse().getOrderList();

        if (!CollectionUtils.isEmpty(orderList)) {
            // 先组合解密
            List<DataListItem> jiemiList = new ArrayList<DataListItem>();

            for (OrderListGetResponseOrderListItem orderListItem : orderList) {
                DataListItem item = new DataListItem();
                item.setDataTag(orderListItem.getOrderSn());
                item.setEncryptedData(orderListItem.getAddress());
                jiemiList.add(item);
                DataListItem item3 = new DataListItem();
                item3.setDataTag(orderListItem.getOrderSn());
                item3.setEncryptedData(orderListItem.getReceiverPhone());
                jiemiList.add(item3);
                DataListItem item4 = new DataListItem();
                item4.setDataTag(orderListItem.getOrderSn());
                item4.setEncryptedData(orderListItem.getReceiverName());
                jiemiList.add(item4);
            }
            // 开始解密

            PddOpenDecryptBatchRequest requestJiemi = new PddOpenDecryptBatchRequest();
            requestJiemi.setDataList(jiemiList);
            PddOpenDecryptBatchResponse responseJiemi = client.syncInvoke(requestJiemi, getAccessToken());
            if (responseJiemi.getErrorResponse() == null) {
                List<OpenDecryptBatchResponseDataDecryptListItem> dataDecryptList = responseJiemi
                        .getOpenDecryptBatchResponse().getDataDecryptList();
                // 解密不能为空
                // 继续循环地址
                for (OrderListGetResponseOrderListItem orderListItem : orderList) {
                    String orderId = orderListItem.getOrderSn();
                    Integer refundStatus = orderListItem.getRefundStatus();
                    SzmCOrderMain szmCOrderMain = szmCOrderMainMapper.selectByOrderNum(orderId);

                    if (null != szmCOrderMain) {
                        if (szmCOrderMain.getOrderStatus() >= 1) {
                            // 看看订单状态是不是取消了
                            if (!refundStatus.equals(1)) {
                                SmzCOrderReturns smzCOrderReturns = smzCOrderReturnsMapper.selectByOrderNum(orderId);
                                if (null == smzCOrderReturns) {
                                    smzCOrderReturns = new SmzCOrderReturns();
                                    smzCOrderReturns.setOrderReturnsDelStart(1);
                                    smzCOrderReturns.setProcessstate(refundStatus.equals(4) ? 1 : 0);// 退款状态
                                    smzCOrderReturns.setConsigneerealName(szmCOrderMain.getUserName());
                                    smzCOrderReturns.setConsigneetelPhone(szmCOrderMain.getUserPhone());
                                    smzCOrderReturns.setReturnSamount(Double.parseDouble(szmCOrderMain.getR1()));
                                    smzCOrderReturns.setStoreId(Long.parseLong(szmCOrderMain.getR2()));
                                    smzCOrderReturns.setUserId(Long.parseLong(szmCOrderMain.getCreateIden()));
                                    smzCOrderReturns.setR4(szmCOrderMain.getOrderStatus().toString());
                                    smzCOrderReturns.setOrderMainId(szmCOrderMain.getOrderMainId());
                                    smzCOrderReturns.setOrderDetailsId(orderId);
                                    smzCOrderReturns.setReturnsType("退款");
                                    smzCOrderReturns.setLogisticsDescription("其他");
                                    smzCOrderReturnsMapper.insert(smzCOrderReturns);
                                    szmCOrderMain.setOrderStatus(8);
                                    szmCOrderMainMapper.updateByPrimaryKey(szmCOrderMain);

                                    StoreMsg storeMsg = new StoreMsg();
                                    storeMsg.setStoreMsgModel("退款/退货通知");// 模块名称
                                    storeMsg.setStoreId(Long.parseLong(szmCOrderMain.getR2()));// 商户id
                                    storeMsg.setModelUrl("orderAdmin");// 模块地址
                                    storeMsg.setUserId(Long.parseLong(szmCOrderMain.getCreateIden()));// 用户id
                                    StringBuffer stringBuffer = new StringBuffer();
                                    stringBuffer.append("您的客户 ");
                                    SzmCUserinfo szmCUserinfo = szmCUserinfoMapper
                                            .selectBrUserId(Long.parseLong(szmCOrderMain.getCreateIden()));
                                    if (Validator.isEmpty(szmCUserinfo.getR3())) {
                                        stringBuffer.append(szmCOrderMain.getUserName());
                                    } else {
                                        stringBuffer.append(szmCUserinfo.getR3());
                                    }
                                    stringBuffer.append(" 于");
                                stringBuffer.append(cn.hutool.core.date.DateUtil.formatDateTime(new Date()));
                                    if (smzCOrderReturns.getReturnsType().equals("退货退款")) {
                                        stringBuffer.append("发起了订单退货申请，请点击前往处理！");
                                    } else {
                                        stringBuffer.append("发起了订单退款，请点击前往处理！");
                                    }
                                    storeMsg.setContent(stringBuffer.toString());// 内容
                                    storeMsg.setReadState(0);// 已读 1 未读 0
                                    storeMsg.setSource(2);// 来源 1 待处理订单，2退款，3 购买水票，4退桶，5借物资，6 还物资，7 使用水票
                                    storeMsg.setDelState(0);// 删除状态
                                    storeMsg.setR1("pages/mine/after-sales/after-sales");// 小程序路径
                                    storeMsg.setR2(smzCOrderReturns.getOrderDetailsId());// 退货id
                                    storeMsgMapper.insert(storeMsg);
                                } else {
                                    if (refundStatus.equals(4)) {

                                        smzCOrderReturns.setProcessstate(1);
                                        smzCOrderReturnsMapper.updateByPrimaryKey(smzCOrderReturns);
                                        szmCOrderMain.setOrderStatus(8);
                                        szmCOrderMainMapper.updateByPrimaryKey(szmCOrderMain);
                                    } else if (refundStatus.equals(1)) {
                                        smzCOrderReturns.setProcessstate(2);
                                        smzCOrderReturnsMapper.updateByPrimaryKey(smzCOrderReturns);
                                        szmCOrderMain.setOrderStatus(2);
                                        szmCOrderMainMapper.updateByPrimaryKey(szmCOrderMain);
                                    } else {
                                        // 退款中
                                        smzCOrderReturns.setProcessstate(0);
                                        smzCOrderReturnsMapper.updateByPrimaryKey(smzCOrderReturns);
                                        szmCOrderMain.setOrderStatus(8);
                                        szmCOrderMainMapper.updateByPrimaryKey(szmCOrderMain);

                                    }
                                }
                            }
                        }
                        continue;
                    }

                    List<OpenDecryptBatchResponseDataDecryptListItem> dataDecryptListChild = dataDecryptList == null ? 
                            new ArrayList<>() : dataDecryptList.stream()
                            .filter(e -> e != null && e.getDataTag() != null && e.getDataTag().equals(orderListItem.getOrderSn()))
                            .collect(Collectors.toList());

                    // 使用更安全的Optional处理方式获取地址
                    String address = "";
                    String mobile = "";
                    String username = "";
                    
                    if (!CollectionUtils.isEmpty(dataDecryptListChild)) {
                        // 获取地址
                        Optional<OpenDecryptBatchResponseDataDecryptListItem> addressItemOpt = dataDecryptListChild.stream()
                                .filter(e -> e != null && e.getDataType() != null && e.getDataType().equals(7))
                                .findFirst();
                        if (addressItemOpt.isPresent()) {
                            OpenDecryptBatchResponseDataDecryptListItem addressItem = addressItemOpt.get();
                            address = addressItem.getDecryptedData() == null ? "" : addressItem.getDecryptedData();
                        }
                        
                        // 获取手机号
                        Optional<OpenDecryptBatchResponseDataDecryptListItem> mobileItemOpt = dataDecryptListChild.stream()
                                .filter(e -> e != null && e.getDataType() != null && e.getDataType().equals(6))
                                .findFirst();
                        if (mobileItemOpt.isPresent()) {
                            OpenDecryptBatchResponseDataDecryptListItem mobileItem = mobileItemOpt.get();
                            String decryptedData = mobileItem.getDecryptedData() == null ? "" : mobileItem.getDecryptedData();
                            String virtualIdentifyNumber = mobileItem.getVirtualIdentifyNumber() == null ? "" : mobileItem.getVirtualIdentifyNumber();
                            mobile = decryptedData + (StringUtils.isNotEmpty(decryptedData) && StringUtils.isNotEmpty(virtualIdentifyNumber) ? "-" : "") + virtualIdentifyNumber;
                        }
                        
                        // 获取用户名
                        Optional<OpenDecryptBatchResponseDataDecryptListItem> usernameItemOpt = dataDecryptListChild.stream()
                                .filter(e -> e != null && e.getDataType() != null && e.getDataType().equals(5))
                                .findFirst();
                        if (usernameItemOpt.isPresent()) {
                            OpenDecryptBatchResponseDataDecryptListItem usernameItem = usernameItemOpt.get();
                            username = usernameItem.getDecryptedData() == null ? "" : usernameItem.getDecryptedData();
                        }
                    }
                    
                    // 保留用户添加的空值检查
                    if(StringUtils.isEmpty(address) || StringUtils.isEmpty(mobile) || StringUtils.isEmpty(username)) {
                        continue;
                    }
                    
                    address = address.contains("[") ? address.split("\\[")[0] : address;
                    String province = address.length() > 3 ? address.substring(0, 3) : "";
                    String city = address.length() > 6 ? address.substring(3, 6) : "";
                    String area = address.length() > 9 ? address.substring(6, 9) : "";
                    Map<String, Object> result = new HashMap<>();
                    try {
                        result = storeIdUtil.determineByWeiLanNoZuobiao( province, city, area, address,address,
                            1);
                    } catch (Exception e) {
                        logger.error("拼多多订单列表:解析storeId异常：" + JSON.toJSONString(e));
                    } finally {
                        result.put("storeId", 586L);
                    }
                    Long storeId = (Long) result.get("storeId");
                    Double longitude = null;
                    Double latitude = null;
                    if (result.get("longitude") != null) {
                        longitude = new BigDecimal(result.get("longitude").toString())
                                .setScale(6, BigDecimal.ROUND_HALF_UP).doubleValue();
                    }
                    if (result.get("latitude") != null) {
                        latitude = new BigDecimal(result.get("latitude").toString())
                                .setScale(6, BigDecimal.ROUND_HALF_UP).doubleValue();
                    }
                    Integer orderNum = 0;

                    SzmCUser szmCUser = szmCUserMapper.selectByPhone(mobile);
                    if (null != szmCUser) {
                        // if (StringUtils.isNotEmpty(szmCUser.getR2()) && szmCUser.getR2().equals("0")) {
                            szmCUser.setR2(storeId.toString());
                            szmCUser.setStoreId(storeId);
                            szmCUserMapper.updateByPrimaryKey(szmCUser);
                        // }
                    } else {
                        // 创建用户
                        szmCUser = new SzmCUser();
                        szmCUser.setUserMobile(mobile);
                        szmCUser.setUserNickname(username);
                        szmCUser.setR2(storeId.toString());
                        szmCUser.setStoreId(storeId);
                        szmCUser.setBindingTime(new Date());
                        szmCUserService.addUser(szmCUser);
                    }

                    SzmCUser szmCUserExtra = szmCUserMapper.selectByPhone(mobile);

                    SzmCAddress szmCAddress = new SzmCAddress();
                    szmCAddress.setUserId(szmCUserExtra.getUserId());
                    szmCAddress.setUserName(szmCUserExtra.getUserNickname());
                    szmCAddress.setTelphoneOne(szmCUserExtra.getUserMobile());
                    szmCAddress.setProvince(address.length() > 3 ? address.substring(0, 3) : "");
                    szmCAddress.setCity(address.length() > 6 ? address.substring(3, 6) : "");
                    szmCAddress.setArea(address.length() > 9 ? address.substring(6, 9) : "");
                    if (address.length() > 9) {
                        String subArray = address.substring(9);
                        szmCAddress.setStreet(subArray);

                    }
                    szmCAddress.setIsDefaultAddress(0);
                    szmCAddress.setState(0);
                    if (latitude != null && longitude != null) {
                        szmCAddress.setR5(latitude + "," + longitude);
                    }
                    szmCAddress.setR1("0");
                    szmCAddress.setR2("1");
                    szmCAddressMapper.insertAddress(szmCAddress);

                    for (PddOrderListGetResponse.OrderListGetResponseOrderListItemItemListItem orderListGetResponseOrderListItemItemListItem : orderListItem
                            .getItemList()) {
                        orderNum += orderListGetResponseOrderListItemItemListItem.getGoodsCount();
                    }

                    // 设置商品信息
                    SzmCOrderMain szmCOrderMainlianying = new SzmCOrderMain();

                    if (latitude != null && longitude != null) {
                        szmCOrderMainlianying.setZuobiao(latitude + "," + longitude);
                        szmCOrderMainlianying.setLat(new BigDecimal(latitude));
                        szmCOrderMainlianying.setLon(new BigDecimal(longitude));
                    }

                    szmCOrderMainlianying.setGroup(0);
                    szmCOrderMainlianying.setUpPrice(0D);
                    szmCOrderMainlianying.setRoyalty(0D);
                    szmCOrderMainlianying.setRemind(0);
                    szmCOrderMainlianying.setCdTypeMoney(0D);
                    szmCOrderMainlianying.setCdMoneyType(0);
                    szmCOrderMainlianying.setIsReturn(0);
                    szmCOrderMainlianying.setIsSms(0);
                    szmCOrderMainlianying.setIsForward(1);
                    szmCOrderMainlianying.setIsInvoice(0);
                    szmCOrderMainlianying.setBucketPrice(0D);
                    szmCOrderMainlianying.setYfMoney(0D);
                    szmCOrderMainlianying.setBack(0);
                    szmCOrderMainlianying.setOrderDiscounts(0D);
                    szmCOrderMainlianying.setFreightPayable(0D);
                    szmCOrderMainlianying.setCdMoney(0D);
                    szmCOrderMainlianying.setCreateIden(szmCUserExtra.getUserId().toString());
                    szmCOrderMainlianying.setUserId(szmCUserExtra.getUserId());

                    szmCOrderMainlianying.setCreateTime(new Date());
                    szmCOrderMainlianying.setPayTime(DateUtils.parseDate(orderListItem.getPayTime()));
                    szmCOrderMainlianying.setOrderNum(orderId);
                    szmCOrderMainlianying.setUserName(username);
                    szmCOrderMainlianying.setUserPhone(mobile);
                    szmCOrderMainlianying.setUserAddress(address);
                    szmCOrderMainlianying.setOrderMoney(0D);
                    szmCOrderMainlianying.setR1("0");
                    szmCOrderMainlianying.setPayNum(orderId);
                    szmCOrderMainlianying.setOrderStatus(2);
                    szmCOrderMainlianying.setIsReplenishment(0);
                    szmCOrderMainlianying.setUserContent(orderListItem.getBuyerMemo());
                    szmCOrderMainlianying.setOrderDelState(0);
                    szmCOrderMainlianying.setR3("0");
                    szmCOrderMainlianying.setR4("[]");
                    szmCOrderMainlianying.setBucketBeans("[]");
                    szmCOrderMainlianying.setR5(orderNum.toString());
                    szmCOrderMainlianying.setR2(szmCUserExtra.getR2());
                    szmCOrderMainlianying.setStoreId(Long.parseLong(szmCUserExtra.getR2()));
                    szmCOrderMainlianying.setPaymentModeId(8L);
                    szmCOrderMainlianying.setDaike(0);
                    szmCOrderMainlianying.setRemind(1);
                    szmCOrderMainlianying.setOrdersource(10);
                    
                    // 根据ordersource获取OrderSource数据，并将settlement_cycle设置到mark字段
                    try {
                        OrderSource orderSource = orderSourceService.selectByPrimaryKey(10);
                        if (orderSource != null && orderSource.getSettlementCycle() != null) {
                            szmCOrderMainlianying.setMark(orderSource.getSettlementCycle());
                        }
                    } catch (Exception e) {
                        logger.error("获取订单来源结算周期失败", e);
                    }
                    
                    szmCOrderMainMapper.insertCancelOrder(szmCOrderMainlianying);

                    for (PddOrderListGetResponse.OrderListGetResponseOrderListItemItemListItem orderListGetResponseOrderListItemItemListItem : orderListItem
                            .getItemList()) {
                        SmzCOrderDetails smzCOrderDetailLianying = new SmzCOrderDetails();
                        smzCOrderDetailLianying.setSource(8);
                        smzCOrderDetailLianying.setProductModelId(null);
                        smzCOrderDetailLianying
                                .setProductSkuname(orderListGetResponseOrderListItemItemListItem.getGoodsSpec() + orderListGetResponseOrderListItemItemListItem.getGoodsName());
                        smzCOrderDetailLianying
                                .setProductSkuimg(orderListGetResponseOrderListItemItemListItem.getGoodsImg());
                        smzCOrderDetailLianying
                                .setOrderProductNum(orderListGetResponseOrderListItemItemListItem.getGoodsCount());
                        orderNum += orderListGetResponseOrderListItemItemListItem.getGoodsCount();
                        // smzCOrderDetailLianying.setOrderDetailsProductPrice(new BigDecimal((Integer)
                        // eee.get("skuJdPrice")).divide(new BigDecimal(100), 2,
                        // RoundingMode.HALF_UP).doubleValue());
                        smzCOrderDetailLianying.setOrderDetailsProductPrice(0D);
                        // smzCOrderDetailLianying.setR1(new BigDecimal((Integer)
                        // eee.get("skuJdPrice")).divide(new BigDecimal(100).multiply(new
                        // BigDecimal((Integer) eee.get("skuCount"))), 2,
                        // RoundingMode.HALF_UP).toString());
                        smzCOrderDetailLianying.setR1("0");
                        smzCOrderDetailLianying.setOrderMainId(orderId);
                        smzCOrderDetailLianying.setR5(orderId + 1);// 子订单编号
                        // smzCOrderDetailLianying.setR4(new BigDecimal((Integer)
                        // eee.get("skuJdPrice")).divide(new BigDecimal(100), 2,
                        // RoundingMode.HALF_UP).toString());
                        smzCOrderDetailLianying.setR4("0");
                        smzCOrderDetailLianying.setIsForward(1);// 是否转单 0是 1否
                        smzCOrderDetailLianying.setStoreId(Long.parseLong(szmCUserExtra.getR2()));
                        smzCOrderDetailLianying.setOrderid(szmCOrderMainlianying.getOrderMainId());
                        smzCOrderDetailsMapper.insertCancelOrder(smzCOrderDetailLianying);
                    }
                    // 通知用户
                    // szmBOrderService.jieDanSms(orderId);
                    // // 通知商家
                    // StoreSmsInfo storeSmsInfo =
                    // storeSmsInfoMapper.selectByStoreId(szmCOrderMainlianying.getStoreId());
                    // if (storeSmsInfo != null) {
                    // SzmCStoreApplyFor szmCStoreApplyFor =
                    // szmCStoreApplyForMapper.selectStoreId(szmCOrderMainlianying.getStoreId());
                    // if (storeSmsInfo.getResidueNum() > 0) {
                    // SmsRelevance smsRelevance =
                    // smsRelevanceMapper.selectByStoreAndMaster(szmCOrderMainlianying.getStoreId(),
                    // 1l);//发起退款
                    // if (smsRelevance != null && smsRelevance.getState() == 1) {
                    // String template = "【水站买】：您有一条待处理的订单，请到订单管理及时处理。";

                    // UtilSMS.sendSMS(szmCStoreApplyFor.getStoreTel(), template);
                    // storeSmsInfo.setResidueNum(storeSmsInfo.getResidueNum() - 1);
                    // storeSmsInfo.setPastNum(storeSmsInfo.getPastNum() + 1);
                    // storeSmsInfoMapper.updateByPrimaryKey(storeSmsInfo);
                    // SmsRecord smsRecord = new SmsRecord();
                    // smsRecord.setStoreId(szmCOrderMainlianying.getStoreId());
                    // SmsMaster smsMaster = smsMasterMapper.selectByPrimaryKey(1l);
                    // smsRecord.setContent(smsMaster.getName());
                    // smsRecordMapper.insert(smsRecord);
                    // }
                    // } else {
                    // RemindSMS.remindSMS(szmCStoreApplyFor.getStoreTel());
                    // }
                    // }
                    LoggerUtil.info("新增订单通知");
                    StoreMsg storeMsg = new StoreMsg();
                    storeMsg.setStoreMsgModel("新订单通知");// 模块名称
                    storeMsg.setStoreId(szmCOrderMainlianying.getStoreId());// 商户id
                    storeMsg.setModelUrl("orderAdmin");// 模块地址
                    storeMsg.setUserId(Long.parseLong(szmCOrderMainlianying.getCreateIden()));// 用户id
                    StringBuffer stringBuffer = new StringBuffer();
                    stringBuffer.append("您的客户 ");
                    SzmCUserinfo szmCUserinfo = szmCUserinfoMapper
                            .selectBrUserId(Long.parseLong(szmCOrderMainlianying.getCreateIden()));
                    if (Validator.isEmpty(szmCUserinfo.getR3())) {
                        stringBuffer.append(szmCOrderMainlianying.getUserName());
                    } else {
                        stringBuffer.append(szmCUserinfo.getR3());
                    }
                    stringBuffer.append(" 于");
                    stringBuffer
                            .append(cn.hutool.core.date.DateUtil.formatDateTime(szmCOrderMainlianying.getCreateTime()));
                    stringBuffer.append("下了一笔订单，请点击前往处理！");
                    storeMsg.setContent(stringBuffer.toString());// 内容
                    storeMsg.setReadState(0);// 已读 1 未读 0
                    storeMsg.setSource(1);// 来源 1 待处理订单，2退款，3 购买水票，4退桶，5借物资，6 还物资，7 使用水票
                    storeMsg.setDelState(0);// 删除状态
                    storeMsg.setR1("pages/orderAdmin/orderAdmin?type=1");// 小程序路径
                    storeMsg.setR2(szmCOrderMainlianying.getOrderNum());// id
                    storeMsg.setR3(szmCOrderMainlianying.getOrderMainId().toString());// id
                    storeMsgMapper.insert(storeMsg);

                    try {
                        // 自动派单送水员
                        Long deliveryUserId = storeIdUtil.determineByWeiLanDeliveryUser( szmCOrderMainlianying.getLat(), 
                        szmCOrderMainlianying.getLon(),szmCOrderMainlianying.getUserAddress(),szmCOrderMainlianying.getStoreId());
                        logger.error("送水员围栏判断，找到送水员id:"+ deliveryUserId);
                    if (deliveryUserId != null) {
                            szmBOrderService.selectDeliveryId(szmCOrderMainlianying.getOrderNum(), deliveryUserId, 0D, 0D, 0D, 0D);
                        }
                    } catch (Exception e) {
                        logger.error("自动派单送水员失败", e);
                    }
                }
            }
        }
        // 休息15秒
        Thread.sleep(10000);
        if (response.getOrderListGetResponse().getHasNext()) {
            return getOrder(startTime, endTime, pageNow + 1);
        }
        return new ResultBean().success(response);
    }

    @GetMapping("jiemi")
    public ResultBean jiemi(String orderId, String encryptedData) throws Exception {

        PopClient client = new PopHttpClient(clientId, clientSecret);

        PddOpenDecryptBatchRequest request = new PddOpenDecryptBatchRequest();
        List<DataListItem> dataList = new ArrayList<DataListItem>();

        DataListItem item = new DataListItem();
        item.setDataTag(orderId);
        item.setEncryptedData(encryptedData);
        dataList.add(item);
        request.setDataList(dataList);
        PddOpenDecryptBatchResponse response = client.syncInvoke(request, getAccessToken());
        System.out.println(JSON.toJSONString(response));
        return new ResultBean().success(JSON.toJSONString(response));
    }

    @PostMapping("refund")
    public ResultBean refund(@RequestBody String body) {
        JSONObject jsonObject = JSON.parseObject(body);
        String body1 = jsonObject.getString("body");
        if (StringUtils.isNotEmpty(body1)) {
            String[] split = body1.split("[\\r\\n]+");
            for (String orderId : split) {

                SzmCOrderMain szmCOrderMain = szmCOrderMainMapper.selectByOrderNum(orderId);
                if (null != szmCOrderMain) {
                    if (szmCOrderMain.getOrderStatus() >= 1) {
                        // 看看订单状态是不是取消了
                        SmzCOrderReturns smzCOrderReturns = smzCOrderReturnsMapper.selectByOrderNum(orderId);
                        if (null == smzCOrderReturns) {
                            smzCOrderReturns = new SmzCOrderReturns();
                            smzCOrderReturns.setOrderReturnsDelStart(1);
                            smzCOrderReturns.setProcessstate(1);// 退款状态
                            smzCOrderReturns.setConsigneerealName(szmCOrderMain.getUserName());
                            smzCOrderReturns.setConsigneetelPhone(szmCOrderMain.getUserPhone());
                            smzCOrderReturns.setReturnSamount(Double.parseDouble(szmCOrderMain.getR1()));
                            smzCOrderReturns.setStoreId(Long.parseLong(szmCOrderMain.getR2()));
                            smzCOrderReturns.setUserId(Long.parseLong(szmCOrderMain.getCreateIden()));
                            smzCOrderReturns.setR4(szmCOrderMain.getOrderStatus().toString());
                            smzCOrderReturns.setOrderMainId(szmCOrderMain.getOrderMainId());
                            smzCOrderReturns.setOrderDetailsId(orderId);
                            smzCOrderReturns.setReturnsType("退款");
                            smzCOrderReturns.setLogisticsDescription("其他");
                            smzCOrderReturnsMapper.insert(smzCOrderReturns);
                            szmCOrderMain.setOrderStatus(8);
                            szmCOrderMainMapper.updateByPrimaryKey(szmCOrderMain);
                        }
                    }
                }
            }
        }

        return new ResultBean().success();
    }

    @PostMapping("/importExcel")
    public ResultBean importExcel(HttpServletResponse response, @RequestParam MultipartFile file) throws IOException {
        EasyExcelListener easyExcelListener = new EasyExcelListener(pinduoduoOrderImportService,
                PinduoduoOrderDto.class);
        EasyExcelFactory.read(file.getInputStream(), PinduoduoOrderDto.class, easyExcelListener).sheet().doRead();
        List<ExcelCheckErrDto<PinduoduoOrderDto>> errList = easyExcelListener.getErrList();
        if (!errList.isEmpty()) {
            // 如果包含错误信息就导出错误信息
            List<PinduoduoOrderDtoErrDto> excelErrDtos = errList.stream().map(excelCheckErrDto -> {
                PinduoduoOrderDtoErrDto userExcelErrDto = JSON.parseObject(JSON.toJSONString(excelCheckErrDto.getT()),
                        PinduoduoOrderDtoErrDto.class);
                userExcelErrDto.setErrMsg(excelCheckErrDto.getErrMsg());
                return userExcelErrDto;
            }).collect(Collectors.toList());
            EasyExcelUtils.webWriteExcel(response, excelErrDtos, PinduoduoOrderDtoErrDto.class, "拼多多导入错误数据");
        }
        return new ResultBean().success();
    }

    @PostMapping("/importSfExcel")
    public ResultBean importSfExcel(HttpServletResponse response, @RequestParam MultipartFile file) throws IOException {
        EasyExcelListener easyExcelListener = new EasyExcelListener(sfOrderImportService,
                PinduoduoOrderDto.class);
        EasyExcelFactory.read(file.getInputStream(), PinduoduoOrderDto.class, easyExcelListener).sheet().doRead();
        List<ExcelCheckErrDto<PinduoduoOrderDto>> errList = easyExcelListener.getErrList();
        if (!errList.isEmpty()) {
            // 如果包含错误信息就导出错误信息
            List<PinduoduoOrderDtoErrDto> excelErrDtos = errList.stream().map(excelCheckErrDto -> {
                PinduoduoOrderDtoErrDto userExcelErrDto = JSON.parseObject(JSON.toJSONString(excelCheckErrDto.getT()),
                        PinduoduoOrderDtoErrDto.class);
                userExcelErrDto.setErrMsg(excelCheckErrDto.getErrMsg());
                return userExcelErrDto;
            }).collect(Collectors.toList());
            EasyExcelUtils.webWriteExcel(response, excelErrDtos, PinduoduoOrderDtoErrDto.class, "顺丰导入错误数据");
        }
        return new ResultBean().success();
    }

    
    // "available": 1,
    // "id": 415,
    // "logisticsCompany": "商家自行配送",
    // "code": "SIJIAPP"
    // },
    @GetMapping("/getLogisticsCompanies")
    public ResultBean getLogisticsCompanies() throws Exception {
        
        PopClient client = new PopHttpClient(clientId, clientSecret);

        PddLogisticsCompaniesGetRequest request = new PddLogisticsCompaniesGetRequest();
        PddLogisticsCompaniesGetResponse response = client.syncInvoke(request);
        return new ResultBean().success(response);
    }
}
