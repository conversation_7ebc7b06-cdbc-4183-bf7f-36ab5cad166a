import axios from "axios"; // 引用axios
import { MessageBox, Message } from "element-ui";
import Qs from "qs";
import router from "@/router";
let paitResult = "";
// import Store from "@/store"
// import Cookies from "js-cookie"
// import Md5 from "js-md5"

/* eslint-disable no-alert, no-console, no-empty,no-unused-vars */
// axios 配置
// 设置响应超时
axios.defaults.timeout = 500000;
// axios.defaults.baseURL = 'https://easy-mock.com/mock/' // 这是调用数据接口,公共接口url+调用接口名
let httpUrl = window.location.host;
console.log(httpUrl);
let url = "";
if (httpUrl.indexOf("szmsh.") !== -1) {
  // console.log("生产环境", httpUrl)
  axios.defaults.baseURL = "https://szmsh.waterstation.com.cn/szm";
  url = "https://szmsh.waterstation.com.cn/szm";
} else if (httpUrl.indexOf("test.") !== -1) {
  axios.defaults.baseURL = "http://test.waterstation.com.cn/szm";
  url = "http://test.waterstation.com.cn/szm";
} else if (httpUrl.indexOf("haoanjinye.site") !== -1) {
  axios.defaults.baseURL = "http://haoanjinye.site/szm";
  url = "http://haoanjinye.site/szm";
} else {
  // console.log("本地开发环境", httpUrl)

  // axios.defaults.baseURL = "https://waterstation.com.cn:10000"
  // axios.defaults.baseURL = "https://zfb.waterstation.com.cn:10000"
  // axios.defaults.baseURL = "https://*************:10000" // lpm
  // axios.defaults.baseURL = "https://*************:10000"// 邹楚
  // axios.defaults.baseURL = "https://************:10000"// hz
  // axios.defaults.baseURL = "https://**************:10000" // 压测服务
  // axios.defaults.baseURL = "http://localhost:10000"
  url = "http://localhost:10000";

  // axios.defaults.baseURL = "https://way.jd.com" // 这是调用数据接口,公共接口url+调用接口名
}
axios.adornUrl = (actionName) => {
  // 非生产环境 && 开启代理, 接口前缀统一使用[/proxyApi/]前缀做代理拦截!
  return url + actionName;
};

// http request 拦截器，通过这个，我们就可以把token传到后台
axios.interceptors.request.use(
  (config) => {
    /**
     * request 加密方法
     * token 令牌
     * signType 签名算法类型 signType：1 => nonce+token+version+nonce = sign ; signType：2 => version+nonce+token+version= sign ; sign: Md5().toUpperCase()
     * version 版本号
     * timestamp 时间戳
     * nonce 临时随机字符串
     * sign 签名
     */
    console.log("config", config.method);
    if (config.method != "get") {
      if (config.url.indexOf("/ws/geocoder") != -1) {
        config.headers = {
          "Content-Type": "application/x-www-form-urlencoded", // 设置跨域头部
        };
      } else if (config.url.indexOf("/pdd/importExcel") != -1 || config.url.indexOf("/pdd/importSfExcel") != -1) {
        // 对于文件上传，不设置 Content-Type，让浏览器自动设置 multipart/form-data 和 boundary
        config.headers = {};
      } else {
        if (Array.isArray(config.data) || !!config.data.header) {
          if (config.data.header.indexOf("pdd") != -1) {
            config.headers = {
              "Content-Type": "application/json", // 设置json头
              "X-PDD-Pati": config.data.header.split(":")[2],
              "X-PDD-PageCode": config.data.header.split(":")[3],
            };
          } else {
            config.headers = {
              "Content-Type": "application/json", // 设置json头
            };
          }
          delete config.data.header;
        } else {
          config.headers = {
            "Content-Type": "application/x-www-form-urlencoded", // 设置跨域头部
          };
          config.data = Qs.stringify(config.data);
        }
      }
    } else {
      console.log("get请求", config);
    }

    return config;
  },
  (err) => {
    return Promise.reject(err);
  }
);

// http response 拦截器
axios.interceptors.response.use(
  (response) => {
    // console.log('请求拦截返回参数', response)
    if (response.status === 200) {
      // 成功
      let returnCode = response.data.code;
      if (returnCode > 10000 && returnCode < 20000) {
        // console.log('成功', response)
        Message.success(response.data.msg);
      } else if (returnCode >= 20000 && returnCode < 30000) {
        // 只弹窗，不操作
        // console.log('失败1', response)
        Message.error(response.data.msg);
      } else if (returnCode >= 30000 && returnCode < 40000) {
        // 只弹窗，点击跳到登入页
        localStorage.removeItem("userInfo");

        MessageBox.confirm(response.data.msg, "确定登出", {
          confirmButtonText: "重新登录",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          // console.log('此处应退出登录  重新实例化')
          router.push({ path: "/login" });
        });
      }
    }
    return response;
  },
  (error) => {
    // console.log("error", error.toString())
    if (
      error.toString().trim() ===
      "TypeError: Cannot read property 'cancelToken' of null"
    ) {
      localStorage.removeItem("userInfo");
      MessageBox.confirm(
        "会话凭证失效，可以取消继续留在该页面，或者重新登录",
        "确定登出",
        {
          confirmButtonText: "重新登录",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(() => {
        //  console.log('此处应退出登录  重新实例化')
        router.push({ path: "/login" });
      });
    }

    // console.log(error.toString().trim())
    if (error.toString().trim() === "Error: Network Error") {
      MessageBox.alert("网络请求异常，请稍后重试", "出错了", {
        confirmButtonText: "确定",
        callback: (action) => {},
      });
    }
    return Promise.reject(error.response.data);
  }
);

export function uuid() {
  var result = [];
  for (var i = 0; i < 8; i++) {
    var ranNum = Math.ceil(Math.random() * 25); // 生成一个0到25的数字
    // 大写字母'A'的ASCII是65,A~Z的ASCII码就是65 + 0~25;然后调用String.fromCharCode()传入ASCII值返回相应的字符并push进数组里
    result.push(String.fromCharCode(97 + ranNum));
  }
  return result.join("");
}

export default axios;
/**
 * fetch 请求方法
 * @param url
 * @param params
 * @returns {Promise}
 */
export function get(url, params = {}) {
  return new Promise((resolve, reject) => {
    axios
      .get(url, {
        params: params,
      })
      .then((response) => {
        resolve(response.data);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

/**
 * post 请求方法
 * @param url
 * @param data
 * @returns {Promise}
 */
export function post(url, data = {}) {
  return new Promise((resolve, reject) => {
    axios.post(url, data).then(
      (response) => {
        // console.log(response.data.code)
        resolve(response.data);
      },
      (err) => {
        reject(err);
      }
    );
  });
}

/**
 * patch 方法封装
 * @param url
 * @param data
 * @returns {Promise}
 */
export function patch(url, data = {}) {
  return new Promise((resolve, reject) => {
    axios.patch(url, data).then(
      (response) => {
        resolve(response.data);
      },
      (err) => {
        reject(err);
      }
    );
  });
}

/**
 * put 方法封装
 * @param url
 * @param data
 * @returns {Promise}
 */
export function put(url, data = {}) {
  return new Promise((resolve, reject) => {
    axios.put(url, data).then(
      (response) => {
        resolve(response.data);
      },
      (err) => {
        reject(err);
      }
    );
  });
}
