<template>
    <div class="padding-bottom-30">
        <!-- 送水员提成管理 begin ----------- -->
        <div>
            <div class="content-box"></div>
            <div class="cont-cent">
                <el-form :inline="true">
                    <el-form-item>
                        <el-input placeholder="输入订单号/产品/手机号/联系方式/地址搜索" v-model="username" clearable
                            style="width:250px"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-select v-model="ordersource" filterable clearable>
                            <el-option label="全部(订单来源)" value=""></el-option>
                            <el-option v-for="item in ordersourcefilter" :key="item.key" :label="item.value"
                                :value="item.key" :disabled="item.disabled"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-select v-model="deliveryUserId" filterable clearable placeholder="请选择送水员">
                            <el-option label="全部(送水员)" value=""></el-option>
                            <el-option label="水站自送" value="0"></el-option>
                            <el-option v-for="item in deliveryUser" :key="item.deliveryUserId"
                                :label="item.deliveryUserName" :value="item.deliveryUserId">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-date-picker @change="selectDataDate" v-model="dateTime" type="daterange" align="right"
                            unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                            :picker-options="pickerOptions" value-format="yyyy-MM-dd"></el-date-picker>
                    </el-form-item>
                    <el-form-item>
                        <el-date-picker @change="selectDataDateFinish" v-model="dateTimeFinish" type="daterange"
                            align="right" unlink-panels range-separator="至" start-placeholder="开始日期(送达时间)"
                            end-placeholder="结束日期(送达时间)" :picker-options="pickerOptions"
                            value-format="yyyy-MM-dd"></el-date-picker>
                    </el-form-item>
                    <el-form-item>
                    <el-select v-model="selectVal" placeholder="按订单状态筛选" clearable >
                        <el-option v-for="(item, index) in selectOptions" :key="index" :label="item.label"
                        :value="item.value">
                        </el-option>
                    </el-select></el-form-item>
                    <!-- <el-form-item>
                        <el-select v-model="storeId" filterable clearable>
                            <el-option label="全部(商家)" value=""></el-option>
                            <el-option v-for="item in storeList" :key="item.storeId" :label="item.storeAme"
                                :value="item.storeId"></el-option>
                        </el-select>
                    </el-form-item> -->
                    <el-form-item style="margin-left:20px;">
                        <el-button type="primary" @click="search">查询</el-button>
                        <el-button @click="clearSearch">清空筛选条件</el-button>
                        <el-button type="success" icon="el-icon-download" size="mini"
                            @click="exportHandle">导出数据</el-button>
                        <el-button type="primary">
                            <Upload @uploaded="getszmcordermain" :url="'/pdd/importSfExcel'" :name="'订单导入'"></Upload>
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>

            <div>
                <!-- 历史订单表 -->
                <el-table :data="szmcorderlist" key="pledBucket7" :header-cell-style="{
                    'text-align': 'center',
                    'background-color': '#EFF2F7',
                }" :cell-style="{
                    'text-align': 'center',
                    'font-size': '13px',
                    color: '#333C48',
                }" stripe border v-loading="fuckLoading10" element-loading-text="拼命加载中" class="productList"
                    style="width: 100%;">
                    <el-table-column prop="ordersource" label="订单来源">
                        <div slot-scope="scope">
                            <img v-if="scope.row.orderSourceImage" style="width: 40px;height: 40px;"
                                :src="scope.row.orderSourceImage"
                                alt="">
                        </div>
                    </el-table-column>
                    <el-table-column prop="mark" label="标记"></el-table-column>
                    <el-table-column prop="mobile" label="登录手机号"></el-table-column>
                    <el-table-column prop="userName" label="联系人"></el-table-column>
                    <el-table-column prop="userPhone" label="联系方式"></el-table-column>
                    <el-table-column prop="userAddress" label="下单地址"></el-table-column>
                    <el-table-column prop="orderDate" label="下单日期"></el-table-column>
                    <el-table-column prop="finishTime" label="送达日期"></el-table-column>
                    <el-table-column prop="orderNumber" label="订单编码"></el-table-column>
                    <el-table-column label="订单信息" min-width="150">
                        <template slot-scope="scope">
                            <div v-if="scope.row.groupShopList">
                                <div v-for="(item, index) in scope.row.groupShopList" :key="index">
                                    {{ item.groupName }} x {{ item.groupNumber }}
                                </div>
                            </div>
                            <div v-if="scope.row.list && scope.row.list.length > 0">
                                <div v-for="(item, index) in scope.row.list[0].orderShopDeatilList" :key="index">
                                    {{ item.title }} x {{ item.shopNumber }}
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="orderTotalNumber" label="总件数"></el-table-column>
                    <el-table-column prop="orderPrice" label="订单总金额">
                        <template slot-scope="scope">￥{{ scope.row.orderPrice }}</template>
                    </el-table-column>
                    <el-table-column prop="newOrderState" label="状态">
                        <template slot-scope="scope">
                            <span>{{ scope.row.newOrderState }}</span>
                            <span v-if="scope.row.orderReturnStete">({{ scope.row.orderReturnStete }})</span>

                        </template>
                    </el-table-column>
                    <el-table-column prop="deliveryName" label="送水员">
                        <template slot-scope="scope">
                            <span>{{ scope.row.deliverId == 0 ? '水站自送' : scope.row.deliveryName }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="orderState" label="操作" width="200">
                        <template slot-scope="scope">
                            <el-button style="margin-top: 3px;" v-if="(adminStoreInfo.role1 == 4 || adminStoreInfo.storeId == 1841) && scope.row.orderState == 2" type="primary" size="mini" @click="print(scope.row)">发货</el-button>
                            <el-button style="margin-top: 3px;" v-if="(adminStoreInfo.role1 == 4 || adminStoreInfo.storeId == 1841) && scope.row.orderState != 2" type="primary" size="mini" @click="printWaybill(scope.row)">打印顺丰单</el-button>
                            <el-button type="success" size="mini" v-if="scope.row.orderState < 5 && adminStoreInfo.role1 == 4"
                                @click="updatestore(scope.row.orderId)">更换商家</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="padding flex align-items-center justify-content-center">
                <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                    layout="total, prev, pager, next,sizes, jumper" background :current-page="page"
                    :page-sizes="currentPageSizes" :page-size="pageSize" :total="pageTotal"></el-pagination>
            </div>
        </div>
        <ordermainupdatestore v-if="ordermainupdatestoreVisible" ref="ordermainupdatestore"
            @refreshDataList="getszmcordermain">
        </ordermainupdatestore>
    </div>
</template>

<script>
import ordermainupdatestore from "./ordermain-update-store"
import { parseTime } from "@/utils/setMethods"
import { ordersource } from "@/data/common"
export default {
    props: {},
    data() {
        return {
            printSdk: '',
            sfToken: '',
            selectVal: '',
            storeList: [],
            adminStoreInfo: {},
            storeId: '',
            ordermainupdatestoreVisible: false,
            deliveryUserId: '',
            deliveryUser: [],
            ordersourcefilter: ordersource,
            ordersource: '',
            fuckLoading10: false,
            username: '',
            startTime: '',
            endTime: '',
            startTimeFinish: '',
            endTimeFinish: '',
            dateTime: [],
            dateTimeFinish: [],
            orderZt: -1,
            page: 0,
            pageSize: 10,
            pageTotal: 0,
            currentPageSizes: [10, 15, 20], // 每页数据条数
            szmcorderlist: [],
            // 日期数据
            pickerOptions: {

                disabledDate(time) {
                    return time.getTime() > Date.now()
                },
                shortcuts: [
                    {
                        text: "最近一周",
                        onClick(picker) {
                            const end = new Date()
                            const start = new Date()
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
                            picker.$emit("pick", [start, end])
                        }
                    },
                    {
                        text: "最近一个月",
                        onClick(picker) {
                            const end = new Date()
                            const start = new Date()
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                            picker.$emit("pick", [start, end])
                        }
                    },
                    {
                        text: "最近三个月",
                        onClick(picker) {
                            const end = new Date()
                            const start = new Date()
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                            picker.$emit("pick", [start, end])
                        }
                    }
                ]
            },
            selectOptions: [
                {
                    value: 2,
                    label: "待发单"
                },
                {
                    value: 3,
                    label: "待接单"
                },
                {
                    value: 4,
                    label: "已发货"
                },
                {
                    value: 10,
                    label: "已完成"
                },
                {
                    value: 8,
                    label: "退款"
                },
            ],
        }
    },
    computed: {
        tableHeight() {
            // console.log(this.$store.getters.getGlobalHeight)
            let height = Number(this.$store.getters.getGlobalHeight) - 450
            if (height >= 300) {
                return height
            } else {
                return 300
            }
        },
        inTableHeight() {
            let height = this.$store.getters.getGlobalHeight
            if (height >= 400) {
                return parseInt(this.$util.mul(height, 0.5))
            } else {
                return 400
            }
        }
    },
    created() {
        this.adminStoreInfo = JSON.parse(this.Cookies.get("adminStoreInfo"))
        this.getstore();
        this.getszmcordermain()
        this.selectByStoreId()
        // this.initPrinter();
    },
    mounted() { },
    watch: {
        selectDataDate(val) {
            if (val) {
                this.startTime = val[0]
                this.endTime = val[1]
            } else {
                this.startTime = ""
                this.endTime = ""
            }
        }
    },
    methods: {
        getstore() {


            this.$post("/szmcstore/selectallstore", {
                storeId: this.adminStoreInfo.storeId
            }).then((res) => {
                if (res.code == 1) {
                    this.storeList = res.data
                } else {
                    this.storeList = []
                }
            })
        },
        updatestore(id) {
            this.ordermainupdatestoreVisible = true
            this.$nextTick(() => {
                this.$refs.ordermainupdatestore.init(id)
            })
        },
        selectByStoreId() {
            let that = this
            let isurl = "/szmb/deliveryusercontroller/selectByStoreId"
            let o = {
                storeId: that.Cookies.get("storeId"),
            }
            that.$post(isurl, o).then(res => {
                if (res.code == 1) {
                    this.deliveryUser = res.data;
                } else {
                    //   that.$message.error(res.msg)
                }
            })
        },
        selectDataDate(val) {
            if (val) {
                this.startTime = val[0]
                this.endTime = val[1]
            } else {
                this.startTime = ""
                this.endTime = ""
            }
        },
        selectDataDateFinish(val) {
            if (val) {
                this.startTimeFinish = val[0]
                this.endTimeFinish = val[1]
            } else {
                this.startTimeFinish = ""
                this.endTimeFinish = ""
            }
        },
        search() {
            this.page = 0;
            this.getszmcordermain();
        },
        clearSearch() {
            this.page = 0;
            this.startTime = ""
            this.endTime = ""
            this.startTimeFinish = ""
            this.endTimeFinish = ""
            this.getszmcordermain();
        },
        // 分页 每页显示多少条
        handleSizeChange(val) {
            this.pageSize = val
            this.getszmcordermain()
        },
        // 点击的当前页
        handleCurrentChange(val) {
            this.page = val
            this.getszmcordermain()
        },


        exportHandle() {
            var url = this.$axios.adornUrl("/szmcordermaincontroller/export?" + [
                "page=1",
                "pageSize=10000",
                "deliveryUserId=" + this.deliveryUserId,
                "username=" + this.username,
                "startTime=" + this.startTime,
                "endTime=" + this.endTime,
                "startTimeFinish=" + this.startTimeFinish,
                "endTimeFinish=" + this.endTimeFinish,
                "ordersource=" + this.ordersource,
                "storeId=" + this.Cookies.get("storeId"),
                "orderZt=-1",
                "orderStatus=" + this.selectVal
            ].join('&'));
            window.open(url);
        },
        getszmcordermain() {
            let that = this
            let isurl = "/szmcordermaincontroller/findallorder"
            let o = {
                storeId: that.Cookies.get("storeId"),
                username: that.username,
                deliveryUserId: that.deliveryUserId,
                endTime: that.endTime,
                startTime: that.startTime,
                endTimeFinish: that.endTimeFinish,
                startTimeFinish: that.startTimeFinish,
                ordersource: that.ordersource,
                addressId: '',
                orderZt: -1,
                index: that.page,
                orderStatus: that.selectVal,
                pageSize: that.pageSize
            }
            that.fuckLoading10 = true
            that.$post(isurl, o).then((res) => {
                that.fuckLoading10 = false
                if (res.code == 1) {
                    that.szmcorderlist = res.data.list
                    that.pageTotal = res.data.count
                } else {
                    that.szmcorderlist = []
                }
            })
        },

        // 打印相关方法
        initPrinter() {
            this.$post('/szmb/szmborder/sfexpress/gettoken').then((res) => {
                if (res.code == 1) {
                    this.sfToken = res.data;
                    const sdkCallback = result => { };
                    const sdkParams = {
                        env: "sbox", // 生产：pro；沙箱：sbox。不传默认生产，转生产需要修改这里
                        partnerID: "YQDZS7KIXFPK",
                        callback: sdkCallback,
                        notips: true
                    };
                    this.printSdk = new SCPPrint(sdkParams);

                    // 获取打印机列表
                    const getPrintersCallback = result => {
                        if (result.code === 1) {
                            const printers = result.printers;
                            console.log(printers)
                            // 设置默认打印机
                            // var printer = 0;
                            // selectElement.value = printer;
                            printSdk.setPrinter(printers[0]);
                        }
                    };
                    this.printSdk.getPrinters(getPrintersCallback);
                } else {
                    this.$message.error(res.msg)
                }
            })
        },

        print(row) {
            // /szmb/szmborder/sfexpress/createorderfromid
            this.$get('/szmb/szmborder/sfexpress/createorderfromid', {
                orderId: row.orderId,
            }).then((res) => {
                if (res.code == 1) {
                    // 创建顺丰订单成功，继续打印面单
                    this.printWaybill(row);
                } else {
                    this.$message.error(res.msg)
                }
            }).catch((error) => {
                console.error('创建顺丰订单失败:', error);
                this.$message.error('创建顺丰订单失败');
            })
        },

        printWaybill(row) {
            this.$get('/szmb/szmborder/sfexpress/printwaybillbyorderid', {
                orderId: row.orderNumber,
            }).then((res) => {
                if (res.code == 1) {
                    // 获取打印面单成功，处理文件下载
                    const data = res.data;
                    if (data && data.fileUrls && data.fileUrls.length > 0) {
                        // 获取原始响应中的文件信息
                        const originalResponse = data.originalResponse;
                        if (originalResponse && originalResponse.obj && originalResponse.obj.files) {
                            // 遍历所有文件进行下载
                            originalResponse.obj.files.forEach((file, index) => {
                                if (file.url && file.token) {
                                    this.downloadWaybillPdf(file.url, file.token, `面单_${row.orderNumber}_${index + 1}.pdf`);
                                }
                            });
                        } else {
                            // 如果没有原始响应，使用简化的URL列表
                            data.fileUrls.forEach((url, index) => {
                                // 注意：这种情况下没有token，可能无法下载
                                this.$message.warning(`文件${index + 1}缺少授权token，无法下载`);
                            });
                        }
                    } else {
                        this.$message.warning('没有找到可下载的面单文件');
                    }
                } else {
                    this.$message.error(res.msg)
                }
            }).catch((error) => {
                console.error('打印面单请求失败:', error);
                this.$message.error('打印面单请求失败');
            })
        },

        // 下载面单PDF文件
        downloadWaybillPdf(url, token, filename) {
            // 创建一个临时的a标签来下载文件
            const link = document.createElement('a');
            link.style.display = 'none';

            // 使用fetch下载文件，添加授权头
            fetch(url, {
                method: 'GET',
                headers: {
                    'X-Auth-Token': token
                }
            }).then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.blob();
            }).then(blob => {
                // 创建blob URL
                const blobUrl = window.URL.createObjectURL(blob);
                link.href = blobUrl;
                link.download = filename;

                // 添加到DOM并触发下载
                document.body.appendChild(link);
                link.click();

                // 清理
                document.body.removeChild(link);
                window.URL.revokeObjectURL(blobUrl);

                this.$message.success(`面单文件 ${filename} 下载成功`);
            }).catch(error => {
                console.error('下载面单文件失败:', error);
                this.$message.error(`下载面单文件失败: ${error.message}`);
            });
        },
    },
    filters: {
        timestampToTime(val, timestamp) {
            return parseTime(val, timestamp)
        }
    },
    components: {
        ordermainupdatestore,
        Upload: () => import('@/components/upload')
    }
}
</script>

<style lang="scss" scoped>
.headBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 0;
}

.content-box {
    width: 100%;
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.content-item1 {
    display: flex;
    justify-content: flex-start;

    div {
        width: 120px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        border: 1px solid #d8dce5;
        border-bottom: 1px solid #eff2f7;
        background: #eff2f7;
        cursor: pointer;
        font-size: 14px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #949fa8;

        &:first-child {
            border-right: none;
            border-radius: 4px 0px 0px 0px;
        }

        &:nth-child(2) {
            border-right: none;
            border-radius: 0px 0px 0px 0px;
        }

        &:last-child {
            border-radius: 0px 4px 0px 0px;
        }
    }

    .active-item {
        background: #ffffff;
        border-bottom: 1px solid #ffffff;
        font-size: 14px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #1693fd;
    }
}

.content-item2 {
    // width: 600px;
    height: 32px;
    line-height: 32px;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;

    div {
        margin-left: 20px;
        font-size: 16px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #3d4c66;
    }
}

.cont-cent {
    width: 100%;
    border: 1px solid #d8dce5;
    margin-top: -1px;
    border-radius: 0 4px 4px 4px;
    padding: 20px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;

    ::v-deep .el-form {
        margin: 0 !important;
    }

    ::v-deep .el-form-item--small.el-form-item {
        margin-bottom: 0;
    }
}

.royalty-cont {
    padding: 15px 0;
}

// 新增  设计表格 开始---------------------------------
.tableBox {
    .el-input-price {
        width: 120px;
    }
}

.table-head-box {
    width: 100%;
    height: 40px;
    line-height: 40px;
    border: 1px solid #d8dce5;
    background: #eff2f7;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;

    div {
        border-right: 1px solid #d8dce5;
        box-sizing: border-box;
        color: #3d4c66;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 10px;

        &:nth-child(1) {
            width: 36%;
            min-width: 150px;
        }

        &:nth-child(2),
        &:nth-child(3),
        &:nth-child(4),
        &:nth-child(5) {
            width: 16%;
            min-width: 120px;
        }

        &:last-child {
            border-right: none;
        }
    }
}

// 设计表格
.table-box-design {
    width: 100%;
    box-sizing: border-box;

    .table-item {
        width: 100%;
        border: 1px solid #d8dce5;
        margin-bottom: 10px;
        box-sizing: border-box;

        .table-item-header {
            width: 100%;
            height: 40px;
            line-height: 40px;
            background: #eff2f7;
            display: flex;
            align-items: center;
            padding: 0 20px;
            box-sizing: border-box;

            .table-header-input {
                margin-left: 20px;
                display: flex;

                .save-item {
                    margin-left: 10px;
                }
            }

            .table-item-bname {
                min-width: 220px;
            }
        }

        .table-body-line {
            width: 100%;
            display: flex;
            justify-content: space-between;
            min-height: 45px;
            border-top: 1px solid #d8dce5;
            box-sizing: border-box;

            ::v-deep .el-input.is-disabled .el-input__inner {
                background-color: #ffffff;
                border-color: #e4e7ed;
                color: #3d4c66;
                cursor: not-allowed;
            }

            &>div {
                border-right: 1px solid #d8dce5;
                box-sizing: border-box;
                color: #3d4c66;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 10px;
                font-size: 16px;
                font-family: Microsoft YaHei;
                font-weight: 400;

                &:nth-child(1) {
                    width: 36%;
                    min-width: 150px;
                }

                &:nth-child(2),
                &:nth-child(3),
                &:nth-child(4),
                &:nth-child(5) {
                    width: 16%;
                    min-width: 120px;
                }

                &:last-child {
                    border-right: none;
                }
            }
        }
    }
}

::v-deep {
    .el-scrollbar {
        width: 100%;
        overflow: hidden;
        height: 100%;
    }

    .el-scrollbar__wrap {
        overflow: scroll;
        overflow-x: auto;
        height: 100%;
    }

    .el-scrollbar__wrap.default-scrollbar__wrap {
        overflow-x: hidden;
    }
}

.empty-data {
    width: 100%;
    padding: 20px;
    box-sizing: border-box;
    font-size: 16px;
    text-align: center;
    border: 1px solid #d8dce5;
    color: #a5aeb5;
}

.total-box {
    margin-top: 20px;
    width: 100%;
    height: 40px;
    line-height: 40px;
    border: 1px solid #d8dce5;
    background: #fafafa;
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;

    div {
        border-right: 1px solid #d8dce5;
        box-sizing: border-box;
        color: #3d4c66;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 10px;

        &:nth-child(1) {
            width: 36%;
            min-width: 150px;
        }

        &:nth-child(2),
        &:nth-child(3),
        &:nth-child(4),
        &:nth-child(5) {
            width: 16%;
            min-width: 120px;
        }

        &:last-child {
            border-right: none;
        }
    }
}

// 设计表格 结束
.pages-box {
    width: 100%;
    padding: 30px 0 0px;
    text-align: center;
}

.total-box2 {
    margin-top: 20px;
    width: 100%;
    height: 40px;
    line-height: 40px;
    border: 1px solid #d8dce5;
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;

    div {
        border-right: 1px solid #d8dce5;
        box-sizing: border-box;
        color: #3d4c66;
        display: flex;
        align-items: center;
        justify-content: center;

        &:nth-child(1) {
            width: 60px;
        }

        &:last-child {
            border-right: none;
            padding: 0 25px;

            &>span {
                margin-left: 20px;
            }
        }
    }
}

::v-deep .el-tabs__nav-wrap::after {
    height: 1px;
}

// 新加样式
.tab-item-ul {
    width: 100%;
    padding: 20px 0 10px;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .tab-item-li {
        height: 30px;
        line-height: 30px;
        text-align: center;
        box-sizing: border-box;
        font-size: 16px;
        color: #3d4c66;
        cursor: pointer;
    }

    .tab-item-li-active {
        border-bottom: 3px solid #1693fd;
        color: #1693fd;
    }

    .tab-item-line {
        width: 2px;
        height: 20px;
        background: #3d4c66;
        margin: 0 10px;
    }
}

.sp-cont {
    .content-box {
        width: 100%;
        display: flex;
        justify-content: flex-end;
        margin-top: 20px;
    }

    .content-item2 {
        // width: 600px;
        height: 32px;
        line-height: 32px;
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;

        div {
            margin-left: 20px;
            font-size: 16px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #3d4c66;
        }
    }

    .cont-cent {
        width: 100%;
        border: 1px solid #d8dce5;
        margin-top: -1px;
        border-radius: 0 4px 4px 4px;
        padding: 20px;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;

        ::v-deep .el-form {
            margin: 0 !important;
        }

        ::v-deep .el-form-item--small.el-form-item {
            margin-bottom: 0;
        }
    }

    ::v-deep {
        .el-scrollbar {
            width: 100%;
            overflow: hidden;
            height: 100%;
        }

        .el-scrollbar__wrap {
            overflow: scroll;
            overflow-x: auto;
            height: 100%;
        }

        .el-scrollbar__wrap.default-scrollbar__wrap {
            overflow-x: hidden;
        }
    }

    .total-box {
        margin-top: 20px;
        width: 100%;
        height: 40px;
        line-height: 40px;
        border: 1px solid #d8dce5;
        background: #fafafa;
        display: flex;
        justify-content: space-between;
        box-sizing: border-box;

        div {
            border-right: 1px solid #d8dce5;
            box-sizing: border-box;
            color: #3d4c66;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 10px;

            &:nth-child(1) {
                width: 36%;
                min-width: 150px;
            }

            &:nth-child(2),
            &:nth-child(3),
            &:nth-child(4),
            &:nth-child(5) {
                width: 16%;
                min-width: 120px;
            }

            &:last-child {
                border-right: none;
            }
        }
    }

    // 设计表格 结束
    .pages-box {
        width: 100%;
        padding: 30px 0 0px;
        text-align: center;
    }

    .total-box2 {
        margin-top: 20px;
        width: 100%;
        height: 40px;
        line-height: 40px;
        border: 1px solid #d8dce5;
        display: flex;
        justify-content: space-between;
        box-sizing: border-box;

        div {
            border-right: 1px solid #d8dce5;
            box-sizing: border-box;
            color: #3d4c66;
            display: flex;
            align-items: center;
            justify-content: center;

            &:nth-child(1) {
                width: 60px;
            }

            &:last-child {
                border-right: none;
                padding: 0 25px;

                &>span {
                    margin-left: 20px;
                }
            }
        }
    }

    .goodsCard {
        width: 100%;
        padding: 0 15px;
        box-sizing: border-box;
        margin-bottom: 20px;
    }

    .goodsCard>div:nth-child(1) {
        width: 30%;
    }

    .goodsCard>div:nth-child(2) {
        width: 70%;
    }

    .el-card__header {
        padding: 0;
    }

    .detailBox {
        padding: 30px 0px;
        box-sizing: border-box;
        display: flex;
    }

    .box-card {
        width: 49%;
    }

    ::v-deep.el-radio__inner {
        width: 20px;
        height: 20px;
    }

    .clearfix {
        font-weight: bold;
    }

    ::v-deep.el-badge__content.is-fixed {
        right: 0;
    }

    .moneyBox>div {
        width: 250px;
    }

    .radioGroup ::v-deep.el-radio-button--small .el-radio-button__inner {
        padding: 9px 16px;
    }

    .radioGroup ::v-deep.el-badge__content.is-fixed {
        right: 4px;
    }
}

.total-box66 {
    margin-top: 20px;
    width: 100%;
    height: 50px;
    line-height: 50px;
    background: #eff2f7;
    display: flex;
    justify-content: space-between;

    div {
        &:first-child {
            width: 55px;
            text-align: center;
        }

        &:last-child {
            padding: 0 20px;

            span {
                margin-left: 25px;
            }
        }
    }
}

.detailDialog ::v-deep .el-dialog .el-dialog__body {
    padding: 10px 20px 30px;
}

.water-ticket-total {
    margin-top: 20px;
    margin-bottom: -20px;
    text-align: right;
}
</style>
