package com.example.waterstationbuyproducer.szmb.daoru.impl;


import cn.hutool.core.lang.Validator;

import com.alibaba.fastjson.JSON;
import com.example.waterstationbuyproducer.dao.*;
import com.example.waterstationbuyproducer.easyexcel.ExcelCheckErrDto;
import com.example.waterstationbuyproducer.easyexcel.ExcelCheckResult;
import com.example.waterstationbuyproducer.entity.*;

import com.example.waterstationbuyproducer.szmb.daoru.SfOrderImportService;
import com.example.waterstationbuyproducer.szmb.dto.PinduoduoOrderDto;
import com.example.waterstationbuyproducer.szmb.service.hz.order.SzmBOrderService;
import com.example.waterstationbuyproducer.szmc.service.SzmCUserService;
import com.example.waterstationbuyproducer.util.DateUtils;
import com.example.waterstationbuyproducer.util.StoreIdUtil;
import com.example.waterstationbuyproducer.util.logger.LoggerUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Title: ContractImportService
 * <AUTHOR>
 * @Date 2022/11/22 11:50
 */
@Service("sfOrderImportServiceImpl")
public class SfOrderImportServiceImpl implements SfOrderImportService {


    protected static final Log logger = LogFactory.getLog(SfOrderImportServiceImpl.class);
    @Autowired
    private SzmCUserMapper szmCUserMapper;
    @Autowired
    private SzmCUserService szmCUserService;
    @Autowired
    private SzmCAddressMapper szmCAddressMapper;
    @Autowired
    private SzmCOrderMainMapper szmCOrderMainMapper;
    @Autowired
    private SmzCOrderDetailsMapper smzCOrderDetailsMapper;
    @Autowired
    private SzmBOrderService szmBOrderService;


    @Autowired
    private StoreSmsInfoMapper storeSmsInfoMapper;

    @Autowired
    private SmsRelevanceMapper smsRelevanceMapper;

    @Autowired
    private SmsMasterMapper smsMasterMapper;

    @Autowired
    private SmsRecordMapper smsRecordMapper;

    @Autowired
    private StoreMsgMapper storeMsgMapper;

    @Autowired
    private SzmCUserinfoMapper szmCUserinfoMapper;

    @Autowired
    private SzmCStoreApplyForMapper szmCStoreApplyForMapper;
    @Autowired
    private SmzCOrderReturnsMapper smzCOrderReturnsMapper;
    @Autowired
    private StoreIdUtil storeIdUtil;

    @Override
    public ExcelCheckResult checkImportExcel(List<PinduoduoOrderDto> contractDtos) throws Exception  {
        //成功结果集
        List<PinduoduoOrderDto> successList = new ArrayList<>();
        //错误数组
        List<ExcelCheckErrDto<PinduoduoOrderDto>> errList = new ArrayList<>();
        // todo 导入暂时不校验库存
        // 获取会议
        for (PinduoduoOrderDto contractDto : contractDtos) {
            //错误信息
            StringBuilder errMsg = new StringBuilder();

            //根据自己的业务去做判断
            if (StringUtils.isEmpty(errMsg.toString())) {

                SzmCOrderMain szmCOrderMain = szmCOrderMainMapper.selectByOrderNum(contractDto.getOrderNo());
                if (null == szmCOrderMain) {
                    // contractDto.getAddress() 清除空格
                    String address = contractDto.getAddress().replaceAll("\\s*", "");
                    address = address.contains("[") ? address.split("\\[")[0] : address;
                    String province = address.length() > 3 ? address.substring(0, 3) : "";
                    String city = address.length() > 6 ? address.substring(3, 6) : "";
                    String area = address.length() > 9 ? address.substring(6, 9) : "";
                    String street = address.length() > 9 ? address.substring(6) : "";
                    Map<String, Object> result = new HashMap<>();
                    try {
                        result = storeIdUtil.determineByWeiLanNoZuobiao( province, city, area, street,street,
                            1);
                    } catch (Exception e) {
                        logger.error("拼多多订单列表:解析storeId异常：" + JSON.toJSONString(e));
                        result.put("storeId", 586L);
                    }
                    // Long storeId = (Long) result.get("storeId");
                    Long storeId = 1841L;
                    Double longitude = null;
                    Double latitude = null;
                    if (result.get("longitude") != null) {
                        longitude = new BigDecimal(result.get("longitude").toString())
                                .setScale(6, BigDecimal.ROUND_HALF_UP).doubleValue();
                    }
                    if (result.get("latitude") != null) {
                        latitude = new BigDecimal(result.get("latitude").toString())
                                .setScale(6, BigDecimal.ROUND_HALF_UP).doubleValue();
                    }

                    String mobile = contractDto.getMobile() + contractDto.getMobile2();
                    SzmCUser szmCUser = szmCUserMapper.selectByPhone(mobile);
                    if (null != szmCUser) {
                        // if (org.apache.commons.lang3.StringUtils.isNotEmpty(szmCUser.getR2()) && szmCUser.getR2().equals("0")) {
                            szmCUser.setR2(storeId.toString());
                            szmCUser.setStoreId(storeId);
                            szmCUserMapper.updateByPrimaryKey(szmCUser);
                        // }
                    } else {
                        // 创建用户
                        szmCUser = new SzmCUser();
                        szmCUser.setUserMobile(mobile);
                        szmCUser.setUserNickname(contractDto.getUsername());
                        szmCUser.setR2(storeId.toString());
                        szmCUser.setStoreId(storeId);
                        szmCUser.setBindingTime(new Date());
                        szmCUserService.addUser(szmCUser);
                    }

                    SzmCUser szmCUserExtra = szmCUserMapper.selectByPhone(mobile);

                    SzmCAddress szmCAddress = new SzmCAddress();
                    szmCAddress.setUserId(szmCUserExtra.getUserId());
                    szmCAddress.setUserName(szmCUserExtra.getUserNickname());
                    szmCAddress.setTelphoneOne(szmCUserExtra.getUserMobile());
                    szmCAddress.setProvince(province);
                    szmCAddress.setCity(city);
                    szmCAddress.setArea(area);
                    szmCAddress.setStreet(address);
                    szmCAddress.setIsDefaultAddress(0);
                    szmCAddress.setState(0);
                    szmCAddress.setR1("0");
                    szmCAddress.setR2("1");
                    if (latitude != null && longitude != null) {
                        szmCAddress.setR5(latitude + "," + longitude);
                    }
                    szmCAddressMapper.insertAddress(szmCAddress);


                    //设置商品信息
                    SzmCOrderMain szmCOrderMainlianying = new SzmCOrderMain();

                    if (latitude != null && longitude != null) {
                        szmCOrderMainlianying.setZuobiao(latitude + "," + longitude);
                        szmCOrderMainlianying.setLat(new BigDecimal(latitude));
                        szmCOrderMainlianying.setLon(new BigDecimal(longitude));
                    }

                    szmCOrderMainlianying.setGroup(0);
                    szmCOrderMainlianying.setUpPrice(0D);
                    szmCOrderMainlianying.setRoyalty(0D);
                    szmCOrderMainlianying.setRemind(0);
                    szmCOrderMainlianying.setCdTypeMoney(0D);
                    szmCOrderMainlianying.setCdMoneyType(0);
                    szmCOrderMainlianying.setIsReturn(0);
                    szmCOrderMainlianying.setIsSms(0);
                    szmCOrderMainlianying.setIsForward(1);
                    szmCOrderMainlianying.setIsInvoice(0);
                    szmCOrderMainlianying.setBucketPrice(0D);
                    szmCOrderMainlianying.setYfMoney(0D);
                    szmCOrderMainlianying.setBack(0);
                    szmCOrderMainlianying.setOrderDiscounts(0D);
                    szmCOrderMainlianying.setFreightPayable(0D);
                    szmCOrderMainlianying.setCdMoney(0D);
                    szmCOrderMainlianying.setCreateIden(szmCUserExtra.getUserId().toString());
                    szmCOrderMainlianying.setUserId(szmCUserExtra.getUserId());

                    szmCOrderMainlianying.setCreateTime(new Date());
                    szmCOrderMainlianying.setPayTime(DateUtils.parseDate(contractDto.getPayTime()));
                    szmCOrderMainlianying.setOrderNum(contractDto.getOrderNo());
                    szmCOrderMainlianying.setUserName(contractDto.getUsername());
                    szmCOrderMainlianying.setUserPhone(mobile);
                    szmCOrderMainlianying.setUserAddress(contractDto.getAddress());
                    szmCOrderMainlianying.setOrderMoney(0D);
                    szmCOrderMainlianying.setR1("0");
                    szmCOrderMainlianying.setPayNum(contractDto.getOrderNo());
                    szmCOrderMainlianying.setOrderStatus(2);
                    szmCOrderMainlianying.setIsReplenishment(0);
                    szmCOrderMainlianying.setUserContent(contractDto.getRemarks());
                    szmCOrderMainlianying.setOrderDelState(0);
                    szmCOrderMainlianying.setR3("0");
                    szmCOrderMainlianying.setR4("[]");
                    szmCOrderMainlianying.setBucketBeans("[]");
                    szmCOrderMainlianying.setR5(StringUtils.isNotEmpty(contractDto.getNumber()) ? contractDto.getNumber() : "0");
                    szmCOrderMainlianying.setR2(szmCUserExtra.getR2());
                    szmCOrderMainlianying.setStoreId(Long.parseLong(szmCUserExtra.getR2()));
                    szmCOrderMainlianying.setPaymentModeId(8L);
                    szmCOrderMainlianying.setDaike(0);
                    szmCOrderMainlianying.setRemind(1);
                    szmCOrderMainlianying.setOrdersource(0);
                    szmCOrderMainMapper.insertCancelOrder(szmCOrderMainlianying);
                    
                    SmzCOrderDetails smzCOrderDetailLianying = new SmzCOrderDetails();
                    smzCOrderDetailLianying.setSource(0);
                    smzCOrderDetailLianying.setProductModelId(null);
                    smzCOrderDetailLianying.setProductSkuname(contractDto.getProduct());
                    // smzCOrderDetailLianying.setProductSkuname(contractDto.getSpec());
                    // smzCOrderDetailLianying.set(contractDto.getProduct());

                    smzCOrderDetailLianying.setProductSkuimg("");
                    smzCOrderDetailLianying.setOrderProductNum(StringUtils.isNotEmpty(contractDto.getNumber()) ? Integer.parseInt(contractDto.getNumber()) : 0);

//                            smzCOrderDetailLianying.setOrderDetailsProductPrice(new BigDecimal((Integer) eee.get("skuJdPrice")).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).doubleValue());
                    smzCOrderDetailLianying.setOrderDetailsProductPrice(0D);
//                            smzCOrderDetailLianying.setR1(new BigDecimal((Integer) eee.get("skuJdPrice")).divide(new BigDecimal(100).multiply(new BigDecimal((Integer) eee.get("skuCount"))), 2, RoundingMode.HALF_UP).toString());
                    smzCOrderDetailLianying.setR1("0");
                    smzCOrderDetailLianying.setOrderMainId(contractDto.getOrderNo());
                    smzCOrderDetailLianying.setR5(contractDto.getOrderNo() + "1");//子订单编号
//                            smzCOrderDetailLianying.setR4(new BigDecimal((Integer) eee.get("skuJdPrice")).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).toString());
                    smzCOrderDetailLianying.setR4("0");
                    smzCOrderDetailLianying.setIsForward(1);//是否转单 0是 1否
                    smzCOrderDetailLianying.setStoreId(Long.parseLong(szmCUserExtra.getR2()));
                    smzCOrderDetailLianying.setOrderid(szmCOrderMainlianying.getOrderMainId());
                    smzCOrderDetailsMapper.insertCancelOrder(smzCOrderDetailLianying);
                    // 通知用户
                    szmBOrderService.jieDanSms(contractDto.getOrderNo());
                    // 通知商家
                    LoggerUtil.info("新增订单通知");
                    StoreMsg storeMsg = new StoreMsg();
                    storeMsg.setStoreMsgModel("新订单通知");//模块名称
                    storeMsg.setStoreId(szmCOrderMainlianying.getStoreId());//商户id
                    storeMsg.setModelUrl("orderAdmin");//模块地址
                    storeMsg.setUserId(Long.parseLong(szmCOrderMainlianying.getCreateIden()));//用户id
                    StringBuffer stringBuffer = new StringBuffer();
                    stringBuffer.append("您的客户 ");
                    SzmCUserinfo szmCUserinfo = szmCUserinfoMapper.selectBrUserId(Long.parseLong(szmCOrderMainlianying.getCreateIden()));
                    if (Validator.isEmpty(szmCUserinfo.getR3())) {
                        stringBuffer.append(szmCOrderMainlianying.getUserName());
                    } else {
                        stringBuffer.append(szmCUserinfo.getR3());
                    }
                    stringBuffer.append(" 于");
                    stringBuffer.append(cn.hutool.core.date.DateUtil.formatDateTime(szmCOrderMainlianying.getCreateTime()));
                    stringBuffer.append("下了一笔订单，请点击前往处理！");
                    storeMsg.setContent(stringBuffer.toString());//内容
                    storeMsg.setReadState(0);//已读 1 未读 0
                    storeMsg.setSource(1);//来源 1  待处理订单，2退款，3 购买水票，4退桶，5借物资，6 还物资，7 使用水票
                    storeMsg.setDelState(0);//删除状态
                    storeMsg.setR1("pages/orderAdmin/orderAdmin?type=1");//小程序路径
                    storeMsg.setR2(szmCOrderMainlianying.getOrderNum());//id
                    storeMsg.setR3(szmCOrderMainlianying.getOrderMainId().toString());//id
                    storeMsgMapper.insert(storeMsg);
                }
            } else {//添加错误信息
                errList.add(new ExcelCheckErrDto(contractDto, errMsg.toString()));
            }
        }
        return new ExcelCheckResult(successList, errList);
    }
}
